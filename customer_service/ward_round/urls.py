from django.urls import path

from customer_service.ward_round.views import MaternitySelectListView, MaternityWardRoundRecordCreateView, MaternityWardRoundRecordDeleteView, MaternityWardRoundRecordDetailView, MaternityWardRoundRecordListView, MaternityWardRoundRecordUpdateView

urlpatterns = [
    
    # 产妇查房记录列表
    path('wardround/maternity/list/', MaternityWardRoundRecordListView.as_view(), name='wardround-list'),
    
    # 产妇查房记录详情
    path('wardround/maternity/detail/<str:rid>/', MaternityWardRoundRecordDetailView.as_view(), name='wardround-detail'),
    
    # 产妇查房记录创建
    path('wardround/maternity/create/', MaternityWardRoundRecordCreateView.as_view(), name='wardround-create'),
    
    # 产妇查房记录更新
    path('wardround/maternity/update/<str:rid>/', MaternityWardRoundRecordUpdateView.as_view(), name='wardround-update'),
    
    # 产妇查房记录删除
    path('wardround/maternity/delete/<str:rid>/', MaternityWardRoundRecordDeleteView.as_view(), name='wardround-delete'),
    
    
    
    
    # 在住产妇选择列表
    path('wardround/maternity-select-list/', MaternitySelectListView.as_view(), name='postpartum-maternity-select-list'),
]